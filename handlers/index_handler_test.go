package handlers

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIndexHandler(t *testing.T) {
	t.Run("Success - redirects to documentation", func(t *testing.T) {
		handler := IndexHandler()
		req := httptest.NewRequest("GET", "/", nil)
		w := httptest.NewRecorder()

		handler(w, req)

		assert.Equal(t, http.StatusFound, w.Code)
		assert.Equal(t, "https://docs.appio.so/", w.<PERSON>er().Get("Location"))
		assert.Equal(t, "text/html; charset=utf-8", w.<PERSON><PERSON>().Get("Content-Type"))
		assert.Contains(t, w.Body.String(), "<!DOCTYPE html>")
		assert.Contains(t, w.Body.String(), "Redirecting to")
		assert.Contains(t, w.Body.String(), "https://docs.appio.so/")
	})

	t.Run("Success - HTML response contains meta refresh", func(t *testing.T) {
		handler := IndexHandler()
		req := httptest.NewRequest("GET", "/", nil)
		w := httptest.NewRecorder()

		handler(w, req)

		assert.Equal(t, http.StatusFound, w.Code)
		body := w.Body.String()
		assert.Contains(t, body, `<meta http-equiv="refresh" content="0;url=https://docs.appio.so/">`)
		assert.Contains(t, body, `<a href="https://docs.appio.so/">https://docs.appio.so/</a>`)
	})

	t.Run("Success - works with different HTTP methods", func(t *testing.T) {
		handler := IndexHandler()
		methods := []string{"GET", "POST", "PUT", "DELETE"}

		for _, method := range methods {
			t.Run("Method: "+method, func(t *testing.T) {
				req := httptest.NewRequest(method, "/", nil)
				w := httptest.NewRecorder()

				handler(w, req)

				assert.Equal(t, http.StatusFound, w.Code)
				assert.Equal(t, "https://docs.appio.so/", w.Header().Get("Location"))
				assert.Equal(t, "text/html; charset=utf-8", w.Header().Get("Content-Type"))
				assert.NotEmpty(t, w.Body.String())
			})
		}
	})

	t.Run("Success - response headers are set correctly", func(t *testing.T) {
		handler := IndexHandler()
		req := httptest.NewRequest("GET", "/", nil)
		w := httptest.NewRecorder()

		handler(w, req)

		// Check that Location header is set before WriteHeader
		assert.Equal(t, "https://docs.appio.so/", w.Header().Get("Location"))
		// Check that Content-Type is set after WriteHeader
		assert.Equal(t, "text/html; charset=utf-8", w.Header().Get("Content-Type"))
		assert.Equal(t, http.StatusFound, w.Code)
	})

	t.Run("Success - HTML structure is valid", func(t *testing.T) {
		handler := IndexHandler()
		req := httptest.NewRequest("GET", "/", nil)
		w := httptest.NewRecorder()

		handler(w, req)

		body := w.Body.String()
		assert.Contains(t, body, "<!DOCTYPE html>")
		assert.Contains(t, body, "<html>")
		assert.Contains(t, body, "<head>")
		assert.Contains(t, body, "</head>")
		assert.Contains(t, body, "<body>")
		assert.Contains(t, body, "</body>")
		assert.Contains(t, body, "</html>")
	})

	t.Run("Success - works with different request paths", func(t *testing.T) {
		handler := IndexHandler()
		paths := []string{"/", "/index", "/home"}

		for _, path := range paths {
			t.Run("Path: "+path, func(t *testing.T) {
				req := httptest.NewRequest("GET", path, nil)
				w := httptest.NewRecorder()

				handler(w, req)

				assert.Equal(t, http.StatusFound, w.Code)
				assert.Equal(t, "https://docs.appio.so/", w.Header().Get("Location"))
				assert.NotEmpty(t, w.Body.String())
			})
		}
	})
}
