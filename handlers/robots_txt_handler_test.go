package handlers

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestRobotsTxtHandler(t *testing.T) {
	t.Run("Success - returns robots.txt content", func(t *testing.T) {
		handler := RobotsTxtHandler()
		req := httptest.NewRequest("GET", "/robots.txt", nil)
		w := httptest.NewRecorder()

		handler(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "text/plain", w.<PERSON><PERSON>().Get("Content-Type"))
		assert.Contains(t, w.Body.String(), "User-agent: *")
		assert.Contains(t, w.Body.String(), "Disallow: /")
	})

	t.Run("Success - content type is text/plain", func(t *testing.T) {
		handler := RobotsTxtHandler()
		req := httptest.NewRequest("GET", "/robots.txt", nil)
		w := httptest.NewRecorder()

		handler(w, req)

		assert.Equal(t, "text/plain", w.<PERSON><PERSON>().Get("Content-Type"))
		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Success - exact robots.txt content", func(t *testing.T) {
		handler := RobotsTxtHandler()
		req := httptest.NewRequest("GET", "/robots.txt", nil)
		w := httptest.NewRecorder()

		handler(w, req)

		expectedContent := `User-agent: *
Disallow: /`
		assert.Equal(t, expectedContent, w.Body.String())
		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Success - works with different HTTP methods", func(t *testing.T) {
		handler := RobotsTxtHandler()
		methods := []string{"GET", "POST", "PUT", "HEAD"}

		for _, method := range methods {
			t.Run("Method: "+method, func(t *testing.T) {
				req := httptest.NewRequest(method, "/robots.txt", nil)
				w := httptest.NewRecorder()

				handler(w, req)

				assert.Equal(t, http.StatusOK, w.Code)
				assert.Equal(t, "text/plain", w.Header().Get("Content-Type"))
				if method != "HEAD" {
					assert.Contains(t, w.Body.String(), "User-agent: *")
					assert.Contains(t, w.Body.String(), "Disallow: /")
				}
			})
		}
	})

	t.Run("Success - response headers are set correctly", func(t *testing.T) {
		handler := RobotsTxtHandler()
		req := httptest.NewRequest("GET", "/robots.txt", nil)
		w := httptest.NewRecorder()

		handler(w, req)

		// Check that Content-Type header is set
		assert.Equal(t, "text/plain", w.Header().Get("Content-Type"))
		assert.Equal(t, http.StatusOK, w.Code)
		assert.NotEmpty(t, w.Body.String())
	})

	t.Run("Success - content format is valid", func(t *testing.T) {
		handler := RobotsTxtHandler()
		req := httptest.NewRequest("GET", "/robots.txt", nil)
		w := httptest.NewRecorder()

		handler(w, req)

		body := w.Body.String()
		lines := []string{
			"User-agent: *",
			"Disallow: /",
		}

		for _, line := range lines {
			assert.Contains(t, body, line)
		}

		// Ensure it's properly formatted robots.txt
		assert.Equal(t, "User-agent: *\nDisallow: /", body)
	})

	t.Run("Success - works with different request paths", func(t *testing.T) {
		handler := RobotsTxtHandler()
		paths := []string{"/robots.txt", "/robots", "/robot.txt"}

		for _, path := range paths {
			t.Run("Path: "+path, func(t *testing.T) {
				req := httptest.NewRequest("GET", path, nil)
				w := httptest.NewRecorder()

				handler(w, req)

				assert.Equal(t, http.StatusOK, w.Code)
				assert.Equal(t, "text/plain", w.Header().Get("Content-Type"))
				assert.Contains(t, w.Body.String(), "User-agent: *")
				assert.Contains(t, w.Body.String(), "Disallow: /")
			})
		}
	})

	t.Run("Success - response is consistent across calls", func(t *testing.T) {
		handler := RobotsTxtHandler()

		// Make multiple calls to ensure consistency
		for i := 0; i < 3; i++ {
			req := httptest.NewRequest("GET", "/robots.txt", nil)
			w := httptest.NewRecorder()

			handler(w, req)

			assert.Equal(t, http.StatusOK, w.Code)
			assert.Equal(t, "text/plain", w.Header().Get("Content-Type"))
			assert.Equal(t, "User-agent: *\nDisallow: /", w.Body.String())
		}
	})
}
