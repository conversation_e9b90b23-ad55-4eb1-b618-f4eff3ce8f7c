package repositories

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestNewFingerprintRepository(t *testing.T) {
	timeWindow := 5 * time.Minute

	t.Run("Creates repository correctly", func(t *testing.T) {
		repo := NewFingerprintRepository(nil, timeWindow)

		assert.NotNil(t, repo)
		assert.Nil(t, repo.DB) // DB is nil in this test
		assert.Equal(t, timeWindow, repo.timeWindow)
	})

	t.Run("Creates repository with correct type", func(t *testing.T) {
		repo := NewFingerprintRepository(nil, timeWindow)

		assert.NotNil(t, repo)
		assert.IsType(t, &FingerprintRepository{}, repo)
	})

	t.Run("Creates repository with different time windows", func(t *testing.T) {
		testCases := []time.Duration{
			1 * time.Minute,
			10 * time.Minute,
			30 * time.Minute,
			1 * time.Hour,
		}

		for _, tw := range testCases {
			repo := NewFingerprintRepository(nil, tw)
			assert.NotNil(t, repo)
			assert.Equal(t, tw, repo.timeWindow)
		}
	})
}

func TestFingerprintRepository_InterfaceCompliance(t *testing.T) {
	timeWindow := 5 * time.Minute

	t.Run("Repository implements interface", func(t *testing.T) {
		repo := NewFingerprintRepository(nil, timeWindow)

		// Verify that the repository implements the interface
		var _ FingerprintRepositoryInterface = repo

		assert.NotNil(t, repo)
	})

	t.Run("Repository structure validation", func(t *testing.T) {
		repo := NewFingerprintRepository(nil, timeWindow)

		// Verify the repository has the expected structure
		assert.NotNil(t, repo)
		assert.Nil(t, repo.DB) // DB is nil in this test
		assert.Equal(t, timeWindow, repo.timeWindow)
		assert.IsType(t, &FingerprintRepository{}, repo)
	})

	t.Run("Interface method signatures exist", func(t *testing.T) {
		repo := NewFingerprintRepository(nil, timeWindow)

		// This ensures all methods exist with the correct signatures
		// We use interface assignment to verify method existence
		var iface FingerprintRepositoryInterface = repo
		assert.NotNil(t, iface)

		// Verify the repository type
		assert.IsType(t, (*FingerprintRepository)(nil), iface)
	})
}

// Note: The ListBy and Create methods require a database connection to test properly.
// Integration tests would be needed to test the actual database operations.
// For now, we're testing the constructor and ensuring the repository structure is correct.

func TestFingerprintRepository_Methods_Structure(t *testing.T) {
	timeWindow := 5 * time.Minute

	t.Run("ListBy method exists", func(t *testing.T) {
		repo := NewFingerprintRepository(nil, timeWindow)

		// This ensures the ListBy method exists with the correct signature
		// We use interface assignment to verify method existence
		var iface FingerprintRepositoryInterface = repo
		assert.NotNil(t, iface)

		// Verify the repository type
		assert.IsType(t, (*FingerprintRepository)(nil), iface)
	})

	t.Run("Create method exists", func(t *testing.T) {
		repo := NewFingerprintRepository(nil, timeWindow)

		// This ensures the Create method exists with the correct signature
		// We use interface assignment to verify method existence
		var iface FingerprintRepositoryInterface = repo
		assert.NotNil(t, iface)

		// Verify the repository type
		assert.IsType(t, (*FingerprintRepository)(nil), iface)
	})

	t.Run("TimeWindow configuration is preserved", func(t *testing.T) {
		testTimeWindow := 15 * time.Minute
		repo := NewFingerprintRepository(nil, testTimeWindow)

		assert.Equal(t, testTimeWindow, repo.timeWindow)
		
		// Verify that the time window is accessible through the struct
		assert.IsType(t, time.Duration(0), repo.timeWindow)
	})
}
