#!/bin/bash

# Generate mocks for all repository interfaces

echo "Generating mocks..."

cd ..

# Create mocks directory if it doesn't exist
mkdir -p internal/mocks

# Generate mocks for each repository interface (only those used in tests)
go run go.uber.org/mock/mockgen -source=repositories/device_repository.go -destination=internal/mocks/mock_device_repository.go -package=mocks
go run go.uber.org/mock/mockgen -source=repositories/feedback_repository.go -destination=internal/mocks/mock_feedback_repository.go -package=mocks
go run go.uber.org/mock/mockgen -source=repositories/service_repository.go -destination=internal/mocks/mock_service_repository.go -package=mocks
go run go.uber.org/mock/mockgen -source=repositories/widget_repository.go -destination=internal/mocks/mock_widget_repository.go -package=mocks

# Generate mocks for each service interface (only those used in tests)
go run go.uber.org/mock/mockgen -source=services/device_service.go -destination=internal/mocks/mock_device_service.go -package=mocks
go run go.uber.org/mock/mockgen -source=services/featureflag_service.go -destination=internal/mocks/mock_featureflag_service.go -package=mocks
go run go.uber.org/mock/mockgen -source=services/feedback_service.go -destination=internal/mocks/mock_feedback_service.go -package=mocks
go run go.uber.org/mock/mockgen -source=services/fingerprint_service.go -destination=internal/mocks/mock_fingerprint_service.go -package=mocks
go run go.uber.org/mock/mockgen -source=services/notification_service.go -destination=internal/mocks/mock_notification_service.go -package=mocks
go run go.uber.org/mock/mockgen -source=services/service_service.go -destination=internal/mocks/mock_service_service.go -package=mocks
go run go.uber.org/mock/mockgen -source=services/widget_service.go -destination=internal/mocks/mock_widget_service.go -package=mocks

echo "Mocks generated successfully!"
